#!/usr/bin/env python3
"""
一键启动器打包脚本
使用PyInstaller将launcher_preview.py打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("🔧 检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    try:
        import PyQt5
        print(f"✅ PyQt5已安装")
    except ImportError:
        print("❌ PyQt5未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "PyQt5"], check=True)
        print("✅ PyQt5安装完成")

def clean_build():
    """清理之前的构建文件"""
    print("🧹 清理构建文件...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    files_to_clean = ["*.spec"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 已删除目录: {dir_name}")
    
    # 删除spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✅ 已删除文件: {spec_file}")

def create_spec_file():
    """创建PyInstaller spec文件"""
    print("📝 创建spec文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
import sys
import os

block_cipher = None

# 添加系统路径
sys_paths = [
    'E:\\\\IndexTTS-FAST\\\\index-tts-liuyue\\\\py312',
    'E:\\\\IndexTTS-FAST\\\\index-tts-liuyue\\\\py312\\\\DLLs',
    'E:\\\\IndexTTS-FAST\\\\index-tts-liuyue\\\\py312\\\\Lib',
    'E:\\\\IndexTTS-FAST\\\\index-tts-liuyue\\\\py312\\\\Scripts',
]

a = Analysis(
    ['AI/launcher_preview.py'],
    pathex=sys_paths,
    binaries=[
        ('E:\\\\IndexTTS-FAST\\\\index-tts-liuyue\\\\py312\\\\DLLs\\\\*', 'DLLs'),
        ('E:\\\\IndexTTS-FAST\\\\index-tts-liuyue\\\\py312\\\\python312.dll', '.'),
    ],
    datas=[
        ('AI/logo.png', '.'),
        ('AI/app.ico', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.sip',
        'sip',
        'subprocess',
        'os',
        'sys',
        'threading',
        'time',
        'ctypes',
        'ctypes.wintypes',
        '_ctypes',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI语音直播系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，避免DLL问题
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 暂时启用控制台窗口以便调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='AI/app.ico',  # 设置图标
    version=None,
    uac_admin=False,
    uac_uiaccess=False,
)
'''
    
    with open("launcher.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ spec文件创建完成")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    # 使用spec文件构建
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "launcher.spec"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 构建成功！")
        print(result.stdout)
    else:
        print("❌ 构建失败！")
        print("错误输出:")
        print(result.stderr)
        return False
    
    return True

def copy_resources():
    """复制必要的资源文件"""
    print("📁 复制资源文件...")
    
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 查找生成的exe文件
    exe_files = list(dist_dir.glob("*.exe"))
    if not exe_files:
        print("❌ 未找到生成的exe文件")
        return False
    
    exe_file = exe_files[0]
    print(f"✅ 找到可执行文件: {exe_file}")
    
    # 复制到根目录
    target_path = Path("AI语音直播系统.exe")
    if target_path.exists():
        target_path.unlink()
    
    shutil.copy2(exe_file, target_path)
    print(f"✅ 已复制到: {target_path}")
    
    return True

def main():
    """主函数"""
    print("🚀 开始打包AI语音直播系统启动器")
    print("=" * 50)
    
    try:
        # 检查依赖
        check_dependencies()
        
        # 清理构建文件
        clean_build()
        
        # 创建spec文件
        create_spec_file()
        
        # 构建可执行文件
        if not build_executable():
            return False
        
        # 复制资源文件
        if not copy_resources():
            return False
        
        print("=" * 50)
        print("🎉 打包完成！")
        print("✅ 可执行文件: AI语音直播系统.exe")
        print("📁 构建文件位于: dist/")
        
        return True
        
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
