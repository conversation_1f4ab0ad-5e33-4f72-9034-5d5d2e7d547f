(['E:\\IndexTTS-FAST\\AI\\launcher_preview.py'],
 ['E:\\IndexTTS-FAST\\AI'],
 ['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'psutil'],
 [('E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
   0),
  ('E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('app.ico', 'E:\\IndexTTS-FAST\\AI\\app.ico', 'DATA'),
  ('logo.png', 'E:\\IndexTTS-FAST\\AI\\logo.png', 'DATA')],
 '3.10.10 (tags/v3.10.10:aad5f6a, Feb  7 2023, 17:20:36) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('launcher_preview',
   'E:\\IndexTTS-FAST\\AI\\launcher_preview.py',
   'PYSOURCE')],
 [('pkgutil', 'D:\\python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\python310\\lib\\zipimport.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\python310\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('typing', 'D:\\python310\\lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'D:\\python310\\lib\\contextlib.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\python310\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\python310\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\python310\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'D:\\python310\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\python310\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\python310\\lib\\email\\generator.py', 'PYMODULE'),
  ('copy', 'D:\\python310\\lib\\copy.py', 'PYMODULE'),
  ('random', 'D:\\python310\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\python310\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\python310\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\python310\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\python310\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\python310\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\python310\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\python310\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\python310\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\python310\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('struct', 'D:\\python310\\lib\\struct.py', 'PYMODULE'),
  ('bisect', 'D:\\python310\\lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\python310\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\python310\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\python310\\lib\\gettext.py', 'PYMODULE'),
  ('email.charset', 'D:\\python310\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\python310\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\python310\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\python310\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\python310\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\python310\\lib\\argparse.py', 'PYMODULE'),
  ('shutil', 'D:\\python310\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\python310\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\python310\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\python310\\lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\python310\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\python310\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('datetime', 'D:\\python310\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\python310\\lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'D:\\python310\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\python310\\lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\python310\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\python310\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\python310\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\python310\\lib\\textwrap.py', 'PYMODULE'),
  ('email', 'D:\\python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python310\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'D:\\python310\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\python310\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\python310\\lib\\token.py', 'PYMODULE'),
  ('pathlib', 'D:\\python310\\lib\\pathlib.py', 'PYMODULE'),
  ('zipfile', 'D:\\python310\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\python310\\lib\\py_compile.py', 'PYMODULE'),
  ('inspect', 'D:\\python310\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\python310\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\python310\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\python310\\lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\python310\\lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib', 'D:\\python310\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('stringprep', 'D:\\python310\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('ctypes', 'D:\\python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\python310\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('PyQt5',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('threading', 'D:\\python310\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python310\\lib\\_threading_local.py', 'PYMODULE'),
  ('psutil',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('signal', 'D:\\python310\\lib\\signal.py', 'PYMODULE'),
  ('subprocess', 'D:\\python310\\lib\\subprocess.py', 'PYMODULE')],
 [('python310.dll', 'D:\\python310\\python310.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\python310\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\python310\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\python310\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\python310\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python310\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'D:\\python310\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\python310\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'D:\\python310\\python3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY')],
 [],
 [],
 [('app.ico', 'E:\\IndexTTS-FAST\\AI\\app.ico', 'DATA'),
  ('logo.png', 'E:\\IndexTTS-FAST\\AI\\logo.png', 'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统\\base_library.zip',
   'DATA')])
