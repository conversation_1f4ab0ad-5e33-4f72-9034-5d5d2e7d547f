#!/usr/bin/env python3
"""
简化的一键启动器打包脚本
使用PyInstaller一行命令打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    """主函数"""
    print("🚀 开始简化打包AI语音直播系统启动器")
    print("=" * 50)
    
    try:
        # 清理之前的构建
        print("🧹 清理构建文件...")
        for item in ["build", "dist", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # 构建命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",  # 单文件模式
            "--windowed",  # 无控制台窗口
            "--name=AI语音直播系统",
            "--icon=AI/app.ico",
            "--add-data=AI/logo.png;.",
            "--add-data=AI/app.ico;.",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtGui", 
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=PyQt5.sip",
            "--hidden-import=sip",
            "--hidden-import=ctypes",
            "--hidden-import=ctypes.wintypes",
            "--hidden-import=psutil",
            "--paths=E:/IndexTTS-FAST/index-tts-liuyue/py312",
            "--paths=E:/IndexTTS-FAST/index-tts-liuyue/py312/DLLs",
            "--paths=E:/IndexTTS-FAST/index-tts-liuyue/py312/Lib",
            "AI/launcher_preview.py"
        ]
        
        print("🔨 执行打包命令...")
        print(f"命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 复制到根目录
            exe_file = Path("dist/AI语音直播系统.exe")
            if exe_file.exists():
                target = Path("AI语音直播系统.exe")
                if target.exists():
                    target.unlink()
                shutil.copy2(exe_file, target)
                print(f"✅ 已复制到: {target}")
            
            print("🎉 打包完成！")
            return True
        else:
            print("❌ 打包失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
