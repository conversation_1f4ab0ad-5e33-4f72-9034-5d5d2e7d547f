# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['AI\\launcher_preview.py'],
    pathex=['E:/IndexTTS-FAST/index-tts-liuyue/py312', 'E:/IndexTTS-FAST/index-tts-liuyue/py312/DLLs', 'E:/IndexTTS-FAST/index-tts-liuyue/py312/Lib'],
    binaries=[],
    datas=[('AI/logo.png', '.'), ('AI/app.ico', '.')],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'PyQt5.sip', 'sip', 'ctypes', 'ctypes.wintypes', 'psutil'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='AI语音直播系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['AI\\app.ico'],
)
